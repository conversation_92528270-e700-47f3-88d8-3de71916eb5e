"use client"

import { SectionTitle, SectionWrapper } from "@/app/shared/components"
import { PRIVACY_AND_DATA_PROTECTION_POLICY } from "@/config"
import Modal from "./Modal"

interface TermsOfUseModalProps {
  open: boolean
  onClose: () => void
}

const TermsOfUseModal = ({ open, onClose }: TermsOfUseModalProps) => {
  return (
    <Modal open={open} onClose={onClose} maxWidth="lg" fullWidth={true}>
      <div className="max-h-[80vh] overflow-y-auto">
        <SectionWrapper className="font-inter !leading-tight text-zinc-400 ~text-sm/lg !px-0 !pb-0">
          <SectionTitle
            title="Terms of Use - Third-Party Authentication Consent"
            subtitle="LEGAL"
            className="text-black"
          />
          <p>{PRIVACY_AND_DATA_PROTECTION_POLICY.introduction}</p>
          <div className="~mt-5/7 ~space-y-5/7">
            {PRIVACY_AND_DATA_PROTECTION_POLICY.data.map((data, index) => (
              <div key={index}>
                <p className="uppercase text-primary ~mb-3/5">{data.title}</p>
                {data.contentList ? (
                  <ul className="list-disc">
                    {data.contentList.map((list, index) => (
                      <li className="ml-5" key={index}>
                        {list}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p>{data.content}</p>
                )}
              </div>
            ))}
          </div>
        </SectionWrapper>
      </div>
    </Modal>
  )
}

export default TermsOfUseModal
