"use client"

import { cn, formatCurrency } from "@/app/shared/utils"
import { useEffect, useState } from "react"
import { Button } from "../ui"
import {
  FieldConfigs,
  FormValues,
  LineItem,
  QuoteResultDetailsProps,
} from "./types/quote-result.types"
import VehicleDetails from "./VehicleDetails"

import { PhoneIcon, WhatsappIcon } from "../../../Icons"
import {
  formatValue,
  getBackgroundStyles,
  getFieldNameFromItem,
  getItemStyles,
} from "./utils/quote-result.utils"

// Single line item row
const LineItemRow: React.FC<{
  item: LineItem
  onValueChange?: (newValue: number) => void
  stepValue?: number
  maxValue?: number
  fieldName?: string
}> = ({ item, onValueChange, stepValue = 0.5, maxValue }) => {
  // Format value based on type
  const formattedValue = formatValue(item.value, item.estimationType)

  return (
    <div
      className={cn(
        "flex justify-between rounded px-3 py-2 transition-all duration-200",
        getBackgroundStyles(item.lineItemType),
      )}
    >
      <div className={cn(getItemStyles(item.lineItemType), "flex-1")}>
        {item.name}
        {item.description && (
          <span className='ml-1 inline-block text-[10px] text-xs text-gray-500'>
            ({item.description})
          </span>
        )}
        {item.isRecommendedValue && (
          <span className='ml-1 inline-block rounded-full bg-green-100 px-1.5 py-0.5 text-[10px] text-xs font-medium text-green-800'>
            Recommended
          </span>
        )}
      </div>

      {onValueChange ? (
        <div className='flex items-center'>
          <Button
            onClick={() => onValueChange(Math.max(0, item.value - stepValue))}
            className='flex h-5 w-5 items-center justify-center rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300'
            disabled={item.value <= 0}
          >
            <span className='text-base'>-</span>
          </Button>

          <div
            className={cn(
              getItemStyles(item.lineItemType),
              "min-w-[40px] text-center",
            )}
          >
            {item.showTextValue && item.textValue
              ? item.textValue
              : formattedValue}
          </div>

          <Button
            onClick={() => {
              const newValue = item.value + stepValue
              // Only increment if below max value (if specified)
              if (maxValue === undefined || newValue <= maxValue) {
                onValueChange(newValue)
              }
            }}
            className='flex h-5 w-5 items-center justify-center rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300'
            disabled={maxValue !== undefined && item.value >= maxValue}
          >
            <span className='text-base'>+</span>
          </Button>
        </div>
      ) : (
        <div className={cn(getItemStyles(item.lineItemType), "ml-3")}>
          {item.showTextValue && item.textValue
            ? item.textValue
            : formattedValue}
        </div>
      )}
    </div>
  )
}

const QuoteResultDetails: React.FC<QuoteResultDetailsProps> = ({
  result,
  onRecalculate,
  stepValue = 0.5,
  formValues: _,
  fieldConfigs: externalFieldConfigs,
  serviceName,
  onFormValueChange,
  activeEstimationIndex = 0,
  onEstimationChange,
}) => {
  const [activeTabIndex, setActiveTabIndex] = useState<number>(
    activeEstimationIndex,
  )
  const [hasChanges, setHasChanges] = useState(false)
  const [formValues, setFormValues] = useState<FormValues>({})
  const [lineItems, setLineItems] = useState<LineItem[]>([])

  // Default field configurations - empty by default, will be provided by parent
  const defaultFieldConfigs: FieldConfigs = {}

  // Use external field configs if provided, otherwise use defaults
  const fieldConfigs: FieldConfigs = externalFieldConfigs || defaultFieldConfigs

  // Get the active estimation
  const activeEstimation = result.estimations[activeTabIndex]

  // Update line items when active estimation changes
  useEffect(() => {
    if (activeEstimation) {
      setLineItems(activeEstimation.lineItems)
      setHasChanges(false)
    }
  }, [activeEstimation, activeTabIndex])

  // Handle tab change
  const handleTabChange = (index: number) => {
    setActiveTabIndex(index)
    if (onEstimationChange) {
      onEstimationChange(index)
    }
  }

  // Handle value change for editable line items
  const handleValueChange = (index: number, newValue: number) => {
    const updatedItems = [...lineItems]
    const item = updatedItems[index]
    updatedItems[index] = { ...item, value: newValue }
    setLineItems(updatedItems)

    // If this item maps to a form field, update the form value too
    const fieldName = getFieldNameFromItem(item)
    if (fieldName) {
      // Process the value based on requestPropertyDataType
      let processedValue: string | number = newValue

      if (item.requestPropertyDataType === "number") {
        // Ensure it's a number
        processedValue = Number(newValue)
      } else if (item.requestPropertyDataType === "text") {
        // Convert to string if needed
        processedValue = String(newValue)
      }

      // Update local state
      setFormValues((prev) => ({
        ...prev,
        [fieldName]: processedValue,
      }))

      // Call the external handler if provided
      if (onFormValueChange) {
        onFormValueChange(fieldName, String(processedValue))
      }
    }

    setHasChanges(true)
  }

  // Handle recalculation
  const handleRecalculate = () => {
    if (onRecalculate && hasChanges) {
      onRecalculate(formValues)
    }
  }

  return (
    <div className='flex h-full max-h-[70vh] min-h-[70vh] w-full flex-col'>
      <h2 className='mb-2 text-start text-lg font-semibold md:pl-4 md:text-2xl'>
        {serviceName ? `Estimation for ${serviceName}` : "Quote Details"}
      </h2>

      {/* Tabs for multiple estimations */}
      {result.estimations.length > 1 && (
        <div className='mb-4 flex w-full overflow-x-auto border-b border-gray-200 px-4'>
          {result.estimations.map((estimation, index) => (
            <button
              key={index}
              onClick={() => handleTabChange(index)}
              className={cn(
                "flex-[1_0_200px] border-b-2 px-4 py-2 text-sm font-medium transition-colors",
                activeTabIndex === index
                  ? "border-primary text-primary"
                  : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700",
              )}
            >
              {estimation.name}
            </button>
          ))}
        </div>
      )}

      <div className='mx-auto w-full max-w-4xl flex-1 overflow-auto px-0 py-2 md:px-4'>
        {/* Vehicle Details if there */}
        {activeEstimation.vehicle && (
          <VehicleDetails vehicle={activeEstimation.vehicle} />
        )}

        {/* Line items */}
        <div className='mb-4 w-full space-y-1 rounded-lg bg-white'>
          {lineItems.map((item, index) => {
            const fieldName = getFieldNameFromItem(item)
            const config =
              fieldName && fieldConfigs[fieldName as string]
                ? fieldConfigs[fieldName as string]
                : undefined

            return (
              <LineItemRow
                key={index}
                item={item}
                onValueChange={
                  item.isEditable
                    ? (newValue) => handleValueChange(index, newValue)
                    : undefined
                }
                stepValue={config?.stepValue || stepValue}
                maxValue={config?.maxValue}
                fieldName={fieldName || undefined}
              />
            )
          })}
        </div>

        {activeEstimation.insurance !== undefined && (
          <div className='mb-1 flex w-full justify-between rounded-xl border border-gray-200 bg-gray-50 px-3 py-1.5'>
            <span className='text-lg font-medium'>Transit Risk</span>
            <span className='text-lg font-semibold'>
              {formatCurrency(activeEstimation.insurance)}
            </span>
          </div>
        )}
      </div>

      <div className='sticky bottom-0 bg-white pt-2'>
        <div className='mx-auto flex max-w-4xl items-center justify-between rounded-xl border border-green-200 bg-green-100 px-4 py-2 text-green-800 shadow-sm'>
          <div className='flex items-center gap-2'>
            <span className='text-xl font-bold'>Total Cost</span>
            {hasChanges && onRecalculate && (
              <button
                onClick={handleRecalculate}
                className='flex items-center gap-1 rounded bg-primary px-2 py-1 text-xs font-medium text-white transition-colors hover:bg-primary/90'
              >
                <span className='text-xs'>↻</span>
                Recalculate
              </button>
            )}
          </div>

          <span
            className={cn("text-2xl font-bold", {
              "line-through": hasChanges && onRecalculate,
            })}
          >
            {formatCurrency(activeEstimation.totalCost)}
          </span>
        </div>

        {/* Book Now CTA: Proceed on WhatsApp and Proceed on Call */}
        <div className='mt-4 flex flex-col items-center gap-3 md:flex-row md:justify-center md:gap-6'>
          <a
            href='https://wa.me/+919353529500?text=Hi%2C%20I%20want%20to%20proceed%20with%20my%20quote%20on%20NexMove.'
            target='_blank'
            rel='noopener noreferrer'
            className='w-full'
          >
            <Button
              size='sm'
              className='flex w-full items-center justify-center gap-2 rounded-lg bg-green-500 ~py-1.5/2'
            >
              <WhatsappIcon className='~size-4/6' />
              Proceed on WhatsApp
            </Button>
          </a>

          <a href='tel:+919353529500' className='w-full'>
            <Button
              size='sm'
              className='flex w-full items-center justify-center gap-2 rounded-lg ~py-1.5/2'
            >
              <PhoneIcon className='~size-4/6' />
              Proceed on Call
            </Button>
          </a>
        </div>
      </div>
    </div>
  )
}

export default QuoteResultDetails
